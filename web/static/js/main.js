// 全局变量
let isRecording = false;
let currentTab = 'clients';

// ===== 标签页管理 =====

// 显示指定标签页
function showTab(tabName) {
    // 隐藏所有标签页
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => {
        tab.style.display = 'none';
    });

    // 显示指定标签页
    const targetTab = document.getElementById(tabName + '-tab');
    if (targetTab) {
        targetTab.style.display = 'block';
    }

    // 更新导航栏状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // 设置当前标签页为活跃状态
    const activeLink = document.querySelector(`[onclick="showTab('${tabName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    currentTab = tabName;

    // 根据标签页加载相应数据
    switch (tabName) {
        case 'dashboard':
            if (typeof onDashboardTabShow === 'function') {
                onDashboardTabShow();
            }
            break;
        case 'clients':
            if (currentTab === 'dashboard' && typeof onDashboardTabHide === 'function') {
                onDashboardTabHide();
            }
            refreshClients();
            break;
        case 'browsers':
            if (currentTab === 'dashboard' && typeof onDashboardTabHide === 'function') {
                onDashboardTabHide();
            }
            refreshBrowsers();
            break;
        case 'proxies':
            if (currentTab === 'dashboard' && typeof onDashboardTabHide === 'function') {
                onDashboardTabHide();
            }
            refreshProxies();
            break;
        case 'recordings':
            if (currentTab === 'dashboard' && typeof onDashboardTabHide === 'function') {
                onDashboardTabHide();
            }
            refreshRecordings();
            break;
    }
}

// 刷新客户端列表
async function refreshClients() {
    try {
        const response = await fetch('/api/clients');
        const result = await response.json();
        if (result.code === 0) {
            updateClientList(result.data);
            updateGroupSelect(result.data);
            // 更新播放分组选择器
            if (typeof updatePlayGroupSelect === 'function') {
                updatePlayGroupSelect(result.data);
            }
        } else {
            showError('获取客户端列表失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 更新客户端列表（备用版本，如果client.js中的版本不工作则使用这个）
function updateClientListFallback(clients) {

    const tbody = document.getElementById('clientList');
    tbody.innerHTML = '';

    clients.forEach(client => {
        const tr = document.createElement('tr');
        const statusBadge = client.online ?
            '<span class="badge bg-success">在线</span>' :
            '<span class="badge bg-secondary">离线</span>';

        const lastOnline = client.last_online ?
            new Date(client.last_online).toLocaleString() :
            '-';

        tr.innerHTML = `
            <td>${client.client_id}</td>
            <td>${client.name}</td>
            <td>${client.group || '-'}</td>
            <td>${client.is_master ? '<span class="badge bg-primary">主控</span>' : '<span class="badge bg-info">从控</span>'}</td>
            <td>${statusBadge}</td>
            <td>${lastOnline}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-info" onclick="showClientDetail ? showClientDetail('${client.client_id}') : alert('详情功能需要client.js')" title="查看详情">详情</button>
                    <button class="btn btn-outline-warning" onclick="showEditClientModal ? showEditClientModal(${client.id}) : alert('编辑功能需要client.js')" title="编辑">编辑</button>
                    <button class="btn btn-outline-danger" onclick="deleteClient('${client.client_id}')" title="删除">删除</button>
                </div>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 更新分组选择器
function updateGroupSelect(clients) {
    const select = document.getElementById('groupSelect');
    const groups = new Set(clients.map(client => client.group).filter(Boolean));

    // 保留"全部"选项
    select.innerHTML = '<option value="">全部</option>';

    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        select.appendChild(option);
    });
}

// 移除客户端
async function removeClient(id) {
    return deleteClient(id);
}

// updateClientList函数由client.js提供

// deleteClient函数由client.js提供

// 开始录制
async function startRecording() {
    if (isRecording) {
        showError('已经在录制中');
        return;
    }

    try {
        const response = await fetch('/api/recording/start', {
            method: 'POST'
        });
        const result = await response.json();
        if (result.code === 0) {
            isRecording = true;
            showSuccess('开始录制');
        } else {
            showError('开始录制失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 停止录制
async function stopRecording() {
    if (!isRecording) {
        showError('没有正在进行的录制');
        return;
    }

    try {
        const response = await fetch('/api/recording/stop', {
            method: 'POST'
        });
        const result = await response.json();
        if (result.code === 0) {
            isRecording = false;
            showSuccess('停止录制');
            refreshRecordings();
        } else {
            showError('停止录制失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 刷新录制列表
async function refreshRecordings() {
    try {
        const response = await fetch('/api/recordings');
        const result = await response.json();
        if (result.code === 0) {
            updateRecordingSelect(result.data);
            // 如果在录制标签页，也更新录制列表
            if (typeof updateRecordingList === 'function') {
                updateRecordingList(result.data);
            }
            // 更新播放录制选择器
            if (typeof updatePlayRecordingSelect === 'function') {
                updatePlayRecordingSelect(result.data);
            }
        } else {
            showError('获取录制列表失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 更新录制选择器
function updateRecordingSelect(recordings) {
    const select = document.getElementById('recordingSelect');
    select.innerHTML = '<option value="">选择录制...</option>';

    recordings.forEach(recording => {
        const option = document.createElement('option');
        option.value = recording.id;
        option.textContent = recording.name;
        select.appendChild(option);
    });
}

// 播放录制
async function playRecording() {
    const recordingId = document.getElementById('recordingSelect').value;
    if (!recordingId) {
        showError('请选择要播放的录制');
        return;
    }

    const group = document.getElementById('groupSelect').value;

    try {
        const response = await fetch('/api/recording/play', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                recording_id: recordingId,
                group: group
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('开始播放录制');
        } else {
            showError('播放录制失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 显示成功消息
function showSuccess(message) {
    // TODO: 实现消息提示
    console.log('Success:', message);
}

// 显示错误消息
function showError(message) {
    // TODO: 实现消息提示
    console.error('Error:', message);
}

// ===== 浏览器管理相关函数 =====

// 刷新浏览器列表
async function refreshBrowsers() {
    try {
        const response = await fetch('/api/browsers');
        const result = await response.json();
        if (result.code === 0) {
            updateBrowserList(result.data);
            updateBrowserGroupSelect(result.data);
        } else {
            showError('获取浏览器列表失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 更新浏览器列表
function updateBrowserList(browsers) {
    const tbody = document.getElementById('browserList');
    tbody.innerHTML = '';

    browsers.forEach(browser => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td><input type="checkbox" class="browser-checkbox" value="${browser.browser_id}"></td>
            <td>${browser.name}</td>
            <td>${browser.client_id}</td>
            <td>${browser.group || '-'}</td>
            <td>${browser.proxy || '-'}</td>
            <td>${browser.port || '-'}</td>
            <td><span class="badge bg-secondary">未知</span></td>
            <td>
                <button class="btn btn-sm btn-success" onclick="launchBrowser('${browser.browser_id}')">启动</button>
                <button class="btn btn-sm btn-danger" onclick="closeBrowser('${browser.browser_id}')">关闭</button>
                <button class="btn btn-sm btn-warning" onclick="editBrowser(${browser.id})">编辑</button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteBrowser(${browser.id})">删除</button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 更新浏览器分组选择器
function updateBrowserGroupSelect(browsers) {
    const select = document.getElementById('browserGroupSelect');
    const groups = new Set(browsers.map(browser => browser.group).filter(Boolean));

    select.innerHTML = '<option value="">选择分组...</option>';

    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group;
        option.textContent = group;
        select.appendChild(option);
    });
}

// 显示创建浏览器模态框
async function showCreateBrowserModal() {
    // 加载客户端列表到选择器
    try {
        const response = await fetch('/api/clients');
        const result = await response.json();
        if (result.code === 0) {
            const clientSelect = document.querySelector('#createBrowserForm select[name="client_id"]');
            clientSelect.innerHTML = '<option value="">选择客户端...</option>';
            result.data.forEach(client => {
                const option = document.createElement('option');
                option.value = client.client_id;
                option.textContent = `${client.name} (${client.client_id})`;
                clientSelect.appendChild(option);
            });
        }
    } catch (error) {
        showError('加载客户端列表失败');
    }

    // 加载代理列表到选择器
    try {
        const response = await fetch('/api/proxies');
        const result = await response.json();
        if (result.code === 0) {
            const proxySelect = document.querySelector('#createBrowserForm select[name="proxy"]');
            proxySelect.innerHTML = '<option value="">不使用代理</option>';
            result.data.forEach(proxy => {
                const option = document.createElement('option');
                option.value = `${proxy.hosts}:${proxy.port}`;
                option.textContent = `${proxy.name} (${proxy.hosts}:${proxy.port})`;
                proxySelect.appendChild(option);
            });
        }
    } catch (error) {
        console.warn('加载代理列表失败');
    }

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('createBrowserModal'));
    modal.show();
}

// 创建浏览器配置
async function createBrowser() {
    const form = document.getElementById('createBrowserForm');
    const formData = new FormData(form);

    const data = {
        name: formData.get('name'),
        client_id: formData.get('client_id'),
        group: formData.get('group'),
        proxy: formData.get('proxy'),
        user_dir: formData.get('user_dir'),
        port: formData.get('port'),
        user_agent: formData.get('user_agent'),
        window_size: formData.get('window_size'),
        headless: formData.has('headless'),
        disable_gpu: formData.has('disable_gpu')
    };

    try {
        const response = await fetch('/api/browsers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('浏览器配置创建成功');
            bootstrap.Modal.getInstance(document.getElementById('createBrowserModal')).hide();
            form.reset();
            refreshBrowsers();
        } else {
            showError(result.message || '创建浏览器配置失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 启动浏览器
async function launchBrowser(browserID) {
    try {
        const response = await fetch('/api/browsers/launch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                browser_id: browserID
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('浏览器启动命令已发送');
        } else {
            showError(result.message || '启动浏览器失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 关闭浏览器
async function closeBrowser(browserID) {
    try {
        const response = await fetch('/api/browsers/close', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                browser_id: browserID
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('浏览器关闭命令已发送');
        } else {
            showError(result.message || '关闭浏览器失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 删除浏览器配置
async function deleteBrowser(id) {
    if (!confirm('确定要删除该浏览器配置吗？')) {
        return;
    }

    try {
        const response = await fetch(`/api/browsers/${id}`, {
            method: 'DELETE'
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('浏览器配置删除成功');
            refreshBrowsers();
        } else {
            showError(result.message || '删除浏览器配置失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 切换所有浏览器选择状态
function toggleAllBrowsers() {
    const selectAll = document.getElementById('selectAllBrowsers');
    const checkboxes = document.querySelectorAll('.browser-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 启动选中的浏览器
async function launchSelectedBrowsers() {
    const checkboxes = document.querySelectorAll('.browser-checkbox:checked');
    if (checkboxes.length === 0) {
        showError('请选择要启动的浏览器');
        return;
    }

    for (const checkbox of checkboxes) {
        await launchBrowser(checkbox.value);
    }
}

// 关闭选中的浏览器
async function closeSelectedBrowsers() {
    const checkboxes = document.querySelectorAll('.browser-checkbox:checked');
    if (checkboxes.length === 0) {
        showError('请选择要关闭的浏览器');
        return;
    }

    for (const checkbox of checkboxes) {
        await closeBrowser(checkbox.value);
    }
}

// 按分组启动浏览器
async function launchBrowsersByGroup() {
    const group = document.getElementById('browserGroupSelect').value;
    if (!group) {
        showError('请选择分组');
        return;
    }

    try {
        const response = await fetch('/api/browsers/launch/group', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                group: group
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('分组浏览器启动命令已发送');
        } else {
            showError(result.message || '启动分组浏览器失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 按分组关闭浏览器
async function closeBrowsersByGroup() {
    const group = document.getElementById('browserGroupSelect').value;
    if (!group) {
        showError('请选择分组');
        return;
    }

    try {
        const response = await fetch('/api/browsers/close/group', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                group: group
            })
        });
        const result = await response.json();
        if (result.code === 0) {
            showSuccess('分组浏览器关闭命令已发送');
        } else {
            showError(result.message || '关闭分组浏览器失败');
        }
    } catch (error) {
        showError('网络错误');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 默认显示监控仪表板
    showTab('dashboard');

    // 也加载其他数据
    refreshClients();
    refreshRecordings();
});